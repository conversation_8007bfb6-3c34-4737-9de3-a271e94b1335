<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disease Analyzer - User Flow Mockup</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #FF8C42;
            font-weight: bold;
        }

        .nav-tabs {
            display: flex;
            gap: 10px;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 8px;
            margin: 20px 30px 0;
        }

        .nav-tab {
            padding: 8px 16px;
            background: transparent;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: rgba(255,255,255,0.2);
        }

        .content {
            padding: 30px;
        }

        .step {
            display: none;
        }

        .step.active {
            display: block;
        }

        .step-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step-number {
            background: #FF8C42;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .progress-indicators {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
        }

        .progress-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #888;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .progress-circle.active {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255,140,66,0.3);
        }

        .progress-line {
            width: 60px;
            height: 2px;
            background: #e0e0e0;
            margin: 0 15px;
            transition: all 0.3s ease;
        }

        .progress-line.active {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
        }

        .pagination-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .pagination-btn {
            width: 45px;
            height: 45px;
            border: 2px solid #e0e0e0;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: #666;
            transition: all 0.3s ease;
        }

        .pagination-btn:hover {
            border-color: #FF8C42;
            color: #FF8C42;
            transform: scale(1.05);
        }

        .pagination-btn:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            transform: none;
        }

        .pagination-btn:disabled:hover {
            border-color: #e0e0e0;
            color: #666;
        }

        .therapy-pagination, .indication-pagination {
            position: relative;
            width: 900px;
            height: 180px;
            overflow: hidden;
            border-radius: 16px;
        }

        .therapy-page, .indication-page {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.4s ease;
        }

        .therapy-page.active, .indication-page.active {
            opacity: 1;
            transform: translateX(0);
        }

        .therapy-page.prev, .indication-page.prev {
            transform: translateX(-100%);
        }

        .continue-btn {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
            color: white;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 30px auto 0;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(255,140,66,0.3);
        }

        .continue-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(255,140,66,0.4);
        }

        .continue-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 15px rgba(255,140,66,0.2);
        }

        .continue-btn:disabled:hover {
            transform: none;
            box-shadow: 0 4px 15px rgba(255,140,66,0.2);
        }

        /* New therapy area icons */
        .oncology-icon {
            background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%);
        }

        .endocrinology-icon {
            background: linear-gradient(135deg, #805ad5 0%, #b794f6 100%);
        }

        .therapy-card {
            flex: 0 0 260px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #e0e0e0;
            border-radius: 16px;
            padding: 25px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .therapy-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #e0e0e0 0%, #e0e0e0 100%);
            transition: all 0.3s ease;
        }

        .therapy-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255,140,66,0.15);
            border-color: #FF8C42;
        }

        .therapy-card:hover::before {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
        }

        .therapy-card.selected {
            border-color: #FF8C42;
            background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(255,140,66,0.2);
        }

        .therapy-card.selected::before {
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
        }

        .therapy-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin: 0 auto 15px;
            transition: all 0.3s ease;
        }

        .psychiatry-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .nephrology-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .dermatology-icon {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .rheumatology-icon {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .other-icon {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        .therapy-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .therapy-count {
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }

        .indication-card {
            flex: 0 0 180px;
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .indication-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #FF8C42;
        }

        .indication-card.selected {
            border-color: #FF8C42;
            background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255,140,66,0.15);
        }

        .indication-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 auto 12px;
            transition: all 0.3s ease;
        }

        /* Psychiatry & Neurology indication icons */
        .adhd-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .schizophrenia-icon {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
        }

        .depression-icon {
            background: linear-gradient(135deg, #4c51bf 0%, #5a67d8 100%);
        }

        .bipolar-icon {
            background: linear-gradient(135deg, #553c9a 0%, #4c51bf 100%);
        }

        /* Nephrology indication icons */
        .nephropathy-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .adpkd-icon {
            background: linear-gradient(135deg, #3182ce 0%, #4facfe 100%);
        }

        .ckd-icon {
            background: linear-gradient(135deg, #2b77cb 0%, #3182ce 100%);
        }

        .aki-icon {
            background: linear-gradient(135deg, #1e40af 0%, #2b77cb 100%);
        }

        /* Dermatology indication icons */
        .ad-icon {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .psoriasis-icon {
            background: linear-gradient(135deg, #f093fb 0%, #fa709a 100%);
        }

        .eczema-icon {
            background: linear-gradient(135deg, #e879f9 0%, #f093fb 100%);
        }

        /* Rheumatology indication icons */
        .ra-icon {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .osteoarthritis-icon {
            background: linear-gradient(135deg, #96e6a1 0%, #a8edea 100%);
        }

        .lupus-icon {
            background: linear-gradient(135deg, #84fab0 0%, #96e6a1 100%);
        }

        /* New indication icons for Oncology */
        .lung-cancer-icon {
            background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%);
        }

        .breast-cancer-icon {
            background: linear-gradient(135deg, #d53f8c 0%, #f687b3 100%);
        }

        .colorectal-icon {
            background: linear-gradient(135deg, #805ad5 0%, #b794f6 100%);
        }

        .leukemia-icon {
            background: linear-gradient(135deg, #c53030 0%, #feb2b2 100%);
        }

        .melanoma-icon {
            background: linear-gradient(135deg, #dd6b20 0%, #fbd38d 100%);
        }

        /* New indication icons for Endocrinology */
        .diabetes-t1-icon {
            background: linear-gradient(135deg, #805ad5 0%, #b794f6 100%);
        }

        .diabetes-t2-icon {
            background: linear-gradient(135deg, #38b2ac 0%, #81e6d9 100%);
        }

        .thyroid-icon {
            background: linear-gradient(135deg, #d69e2e 0%, #faf089 100%);
        }

        .indication-name {
            font-size: 15px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .indication-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .indication-section {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s ease;
        }

        .indication-section.show {
            opacity: 1;
            transform: translateY(0);
        }

        .report-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 4px solid #FF8C42;
        }

        .example-badge {
            background: #dc3545;
            color: white;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .report-title h2 {
            color: #333;
            font-size: 28px;
            font-weight: 700;
            margin: 10px 0;
        }

        .disease-description {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(74, 85, 104, 0.05);
            border-radius: 8px;
            border-left: 3px solid #4a5568;
        }

        .disease-description p {
            color: #555;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
        }

        .insight-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 25px;
            margin-top: 20px;
        }

        .insight-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 4px solid #e0e0e0;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .insight-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .insight-card.expanded {
            grid-column: 1 / -1;
        }

        .disease-overview-card {
            border-left-color: #4a5568;
        }

        .market-evolution-card {
            border-left-color: #38a169;
        }

        .patient-insights-card {
            border-left-color: #3182ce;
        }

        .unmet-need-card {
            border-left-color: #d69e2e;
        }

        .competitive-card {
            border-left-color: #e53e3e;
        }

        .insight-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .insight-header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .insight-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .insight-header h3 {
            color: #333;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .expand-btn {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .expand-btn:hover {
            background: #e9ecef;
            border-color: #FF8C42;
        }

        .expand-btn.expanded {
            background: #FF8C42;
            color: white;
            border-color: #FF8C42;
        }

        .insight-content {
            color: #555;
            line-height: 1.5;
        }

        .insight-summary {
            display: block;
        }

        .insight-details {
            display: none;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        .insight-details.show {
            display: block;
            animation: fadeInUp 0.3s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .report-filters {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .filter-select {
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: white;
            font-size: 14px;
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: #FF8C42;
            box-shadow: 0 0 0 2px rgba(255,140,66,0.2);
        }

        .view-toggle {
            display: flex;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }

        .view-toggle-btn {
            padding: 8px 16px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .view-toggle-btn.active {
            background: #FF8C42;
            color: white;
        }

        .chart-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chart-tab {
            padding: 10px 20px;
            background: transparent;
            border: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
        }

        .chart-tab.active {
            color: #FF8C42;
            border-bottom-color: #FF8C42;
        }

        .chart-content {
            display: none;
        }

        .chart-content.active {
            display: block;
        }

        .mini-chart {
            width: 100%;
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }

        .mini-chart::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M10,80 Q30,20 50,60 Q70,10 90,50" stroke="white" stroke-width="2" fill="none" opacity="0.3"/></svg>');
            background-size: 100% 100%;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-card-value {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-card-label {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }

        .interactive-timeline {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .timeline-controls {
            display: flex;
            gap: 10px;
        }

        .timeline-btn {
            padding: 6px 12px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .timeline-btn.active {
            background: #FF8C42;
            color: white;
            border-color: #FF8C42;
        }

        .timeline-content {
            height: 200px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
        }

        .export-options {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .export-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }

        .progress-tracker {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .progress-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .progress-item:last-child {
            border-bottom: none;
        }

        .progress-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .progress-bar {
            width: 200px;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .quick-insights {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .quick-insights h3 {
            margin-bottom: 15px;
            font-size: 18px;
        }

        .insights-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .insights-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .insight-icon-mini {
            width: 20px;
            height: 20px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .comparison-toggle {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .comparison-options {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }

        .comparison-card {
            flex: 1;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .comparison-card:hover {
            border-color: #FF8C42;
            background: #fff5f0;
        }

        .comparison-card.selected {
            border-color: #FF8C42;
            background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
        }

        .floating-summary {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            max-width: 300px;
            z-index: 1000;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .floating-summary.show {
            transform: translateY(0);
        }

        .floating-summary h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }

        .floating-summary p {
            margin: 0;
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .close-floating {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #999;
        }

        /* Sidebar Navigation Styles */
        .report-layout {
            display: flex;
            gap: 0;
            min-height: 500px;
        }

        .sidebar-nav {
            width: 280px;
            background: #ffffff;
            border-radius: 12px 0 0 12px;
            overflow: hidden;
            box-shadow: inset -1px 0 0 #e5e7eb;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: transparent;
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            font-size: 15px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-item:hover {
            background: #f3f4f6;
            color: #1f2937;
        }

        .nav-item.active {
            background: #1f2937;
            color: white;
            font-weight: 600;
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #FF8C42;
        }

        .nav-icon {
            font-size: 18px;
            width: 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .main-content-area {
            flex: 1;
            background: white;
            border-radius: 0 12px 12px 0;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        @media (max-width: 768px) {
            .report-layout {
                flex-direction: column;
            }
            
            .sidebar-nav {
                width: 100%;
                border-radius: 12px 12px 0 0;
            }
            
            .main-content-area {
                border-radius: 0 0 12px 12px;
            }
        }

        .bookmark-btn {
            background: none;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 6px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
        }

        .bookmark-btn:hover {
            border-color: #FF8C42;
            color: #FF8C42;
        }

        .bookmark-btn.bookmarked {
            background: #FF8C42;
            color: white;
            border-color: #FF8C42;
        }

        /* KPI Popup Modal Styles */
        .kpi-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .kpi-modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .kpi-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .kpi-modal-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }

        .kpi-modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .kpi-modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .kpi-modal-body {
            padding: 30px;
        }

        .kpi-section {
            margin-bottom: 25px;
        }

        .kpi-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .kpi-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border-left: 4px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .kpi-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .kpi-value {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .kpi-label {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }

        .kpi-description {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .kpi-description p {
            margin: 0;
            color: #555;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Chatbot Modal Styles */
        .chatbot-modal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

                 .chatbot-modal-content {
             background-color: white;
             margin: 1% auto;
             padding: 0;
             border-radius: 12px;
             width: 95%;
             max-width: 1200px;
             height: 90vh;
             box-shadow: 0 20px 40px rgba(0,0,0,0.15);
             animation: modalSlideIn 0.3s ease;
             display: flex;
             flex-direction: column;
         }

        .chatbot-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chatbot-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chatbot-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .chatbot-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .chatbot-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .message.bot .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.user .message-avatar {
            background: #FF8C42;
            color: white;
        }

        .message-content {
            background: white;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 70%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            line-height: 1.4;
            font-size: 14px;
        }

        .message.user .message-content {
            background: #FF8C42;
            color: white;
        }

        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 5px;
        }

        .chat-input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102,126,234,0.2);
        }

        .chat-send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .chat-send-btn:hover {
            transform: scale(1.05);
        }

        .chat-send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #ccc;
            border-radius: 50%;
            animation: typingAnimation 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingAnimation {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .suggested-questions {
            padding: 0 20px 15px;
            background: #f8f9fa;
        }

        .suggested-questions h4 {
            margin: 0 0 10px 0;
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }

        .suggestion-chip {
            display: inline-block;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            padding: 6px 12px;
            margin: 3px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

                 .suggestion-chip:hover {
             border-color: #667eea;
             background: #f0f4f8;
         }

         /* Theme Discovery Modal Styles */
         .theme-modal {
             display: none;
             position: fixed;
             z-index: 1002;
             left: 0;
             top: 0;
             width: 100%;
             height: 100%;
             background-color: rgba(0,0,0,0.5);
             backdrop-filter: blur(5px);
         }

         .theme-modal-content {
             background-color: white;
             margin: 2% auto;
             padding: 0;
             border-radius: 12px;
             width: 90%;
             max-width: 1000px;
             height: 85vh;
             box-shadow: 0 20px 40px rgba(0,0,0,0.15);
             animation: modalSlideIn 0.3s ease;
             display: flex;
             flex-direction: column;
         }

         .theme-header {
             background: linear-gradient(135deg, #38a169 0%, #48bb78 100%);
             color: white;
             padding: 20px 30px;
             border-radius: 12px 12px 0 0;
             display: flex;
             justify-content: space-between;
             align-items: center;
         }

         .theme-title {
             font-size: 20px;
             font-weight: 600;
             margin: 0;
             display: flex;
             align-items: center;
             gap: 10px;
         }

         .theme-close {
             background: none;
             border: none;
             color: white;
             font-size: 24px;
             cursor: pointer;
             padding: 0;
             width: 30px;
             height: 30px;
             border-radius: 50%;
             display: flex;
             align-items: center;
             justify-content: center;
             transition: background 0.3s ease;
         }

         .theme-close:hover {
             background: rgba(255,255,255,0.2);
         }

         .theme-body {
             flex: 1;
             padding: 30px;
             overflow-y: auto;
             background: #f8f9fa;
         }

         .theme-intro {
             background: rgba(56, 161, 105, 0.1);
             border-radius: 8px;
             padding: 20px;
             margin-bottom: 25px;
             border-left: 4px solid #38a169;
         }

         .theme-intro h3 {
             margin: 0 0 10px 0;
             color: #2d3748;
             font-size: 16px;
             font-weight: 600;
         }

         .theme-intro p {
             margin: 0;
             color: #4a5568;
             font-size: 14px;
             line-height: 1.5;
         }

         .theme-tree {
             background: white;
             border-radius: 12px;
             padding: 20px;
             box-shadow: 0 4px 15px rgba(0,0,0,0.08);
         }

         .tree-level-1 {
             margin-bottom: 25px;
         }

         .tree-level-1:last-child {
             margin-bottom: 0;
         }

         .level-1-node {
             background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
             color: white;
             padding: 15px 20px;
             border-radius: 10px;
             margin-bottom: 15px;
             cursor: pointer;
             transition: all 0.3s ease;
             display: flex;
             align-items: center;
             justify-content: space-between;
             box-shadow: 0 4px 15px rgba(102,126,234,0.3);
         }

         .level-1-node:hover {
             transform: translateY(-2px);
             box-shadow: 0 6px 20px rgba(102,126,234,0.4);
         }

         .level-1-content {
             display: flex;
             align-items: center;
             gap: 12px;
         }

         .level-1-icon {
             font-size: 20px;
             width: 30px;
             text-align: center;
         }

         .level-1-text {
             font-size: 16px;
             font-weight: 600;
         }

         .expand-indicator {
             font-size: 14px;
             transition: transform 0.3s ease;
         }

         .level-1-node.expanded .expand-indicator {
             transform: rotate(90deg);
         }

         .tree-level-2 {
             display: none;
             margin-left: 20px;
             margin-top: 10px;
         }

         .tree-level-2.show {
             display: block;
             animation: fadeInDown 0.3s ease;
         }

         @keyframes fadeInDown {
             from {
                 opacity: 0;
                 transform: translateY(-10px);
             }
             to {
                 opacity: 1;
                 transform: translateY(0);
             }
         }

         .level-2-node {
             background: #f0f4f8;
             border: 1px solid #e2e8f0;
             border-radius: 8px;
             padding: 12px 15px;
             margin-bottom: 8px;
             cursor: pointer;
             transition: all 0.3s ease;
             display: flex;
             align-items: center;
             justify-content: space-between;
         }

         .level-2-node:hover {
             background: #e6fffa;
             border-color: #38a169;
             transform: translateX(5px);
         }

         .level-2-content {
             display: flex;
             align-items: center;
             gap: 10px;
         }

         .level-2-icon {
             font-size: 16px;
             width: 20px;
             text-align: center;
             color: #4a5568;
         }

         .level-2-text {
             font-size: 14px;
             font-weight: 500;
             color: #2d3748;
         }

         .tree-level-3 {
             display: none;
             margin-left: 15px;
             margin-top: 8px;
         }

         .tree-level-3.show {
             display: block;
             animation: fadeInDown 0.3s ease;
         }

         .level-3-node {
             background: #fff;
             border: 1px solid #e2e8f0;
             border-radius: 6px;
             padding: 8px 12px;
             margin-bottom: 5px;
             transition: all 0.3s ease;
             display: flex;
             align-items: center;
             gap: 8px;
         }

         .level-3-node:hover {
             background: #f7fafc;
             border-color: #cbd5e0;
             transform: translateX(3px);
         }

         .level-3-icon {
             font-size: 12px;
             width: 15px;
             text-align: center;
             color: #718096;
         }

         .level-3-text {
             font-size: 13px;
             color: #4a5568;
             line-height: 1.4;
         }

         .theme-stats {
             display: flex;
             gap: 15px;
             margin-top: 20px;
             padding: 15px;
             background: #f7fafc;
             border-radius: 8px;
         }

         .stat-item {
             flex: 1;
             text-align: center;
         }

         .stat-value {
             font-size: 20px;
             font-weight: 700;
             color: #2d3748;
             margin-bottom: 5px;
         }

         .stat-label {
             font-size: 12px;
             color: #718096;
             text-transform: uppercase;
             letter-spacing: 0.5px;
         }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <div class="logo-icon">DA</div>
                <div>
                    <h1 style="font-size: 24px;">Disease Analyzer</h1>
                    <p style="opacity: 0.9; font-size: 14px;">AI-Powered Pipeline Intelligence Platform</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; gap: 15px;">
                <button id="themeDiscoveryBtn" class="export-btn" onclick="openThemeDiscovery()" style="margin: 0; background: linear-gradient(135deg, #38a169 0%, #48bb78 100%); opacity: 0.5; cursor: not-allowed;" disabled>
                    🔍 Discover Themes
                </button>
                <button id="chatbotBtn" class="export-btn" onclick="openChatbot()" style="margin: 0; opacity: 0.5; cursor: not-allowed;" disabled>
                    🤖 Ask Disease Analyzer
                </button>
            </div>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showStep(1)">1. Therapy Selection</button>
            <button class="nav-tab" onclick="showStep(2)">2. Insight Report</button>
        </div>



        <div class="content">
            <!-- Step 1: Therapy and Indication Selection -->
            <div class="step active" id="step1">
                <!-- Progress Indicators -->
                <div class="progress-indicators">
                    <div class="progress-circle" id="therapyCircle">
                        <span>1</span>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-circle" id="indicationCircle">
                        <span>2</span>
                    </div>
                </div>
                
                <!-- Therapy Area Selection -->
                <div class="therapy-section">
                    <h4 style="color: #333; margin-bottom: 15px; font-size: 18px;">Select Therapy Area</h4>
                    <div class="pagination-container">
                        <button class="pagination-btn prev-btn" id="therapyPrevBtn" onclick="changeTherapyPage(-1)">
                            <span>‹</span>
                        </button>
                        <div class="therapy-pagination">
                            <div class="therapy-page active" id="therapyPage1">
                                <div class="therapy-card" data-therapy="psychiatry" onclick="selectTherapy('psychiatry')">
                                    <div class="therapy-icon psychiatry-icon">🧠</div>
                                    <div class="therapy-name">Psychiatry & Neurology</div>
                                    <div class="therapy-count">4 indications</div>
                                </div>
                                
                                <div class="therapy-card" data-therapy="nephrology" onclick="selectTherapy('nephrology')">
                                    <div class="therapy-icon nephrology-icon">🫘</div>
                                    <div class="therapy-name">Nephrology</div>
                                    <div class="therapy-count">4 indications</div>
                                </div>
                                
                                <div class="therapy-card" data-therapy="dermatology" onclick="selectTherapy('dermatology')">
                                    <div class="therapy-icon dermatology-icon">🌡️</div>
                                    <div class="therapy-name">Dermatology</div>
                                    <div class="therapy-count">3 indications</div>
                                </div>
                            </div>
                            
                            <div class="therapy-page" id="therapyPage2">
                                <div class="therapy-card" data-therapy="rheumatology" onclick="selectTherapy('rheumatology')">
                                    <div class="therapy-icon rheumatology-icon">🦴</div>
                                    <div class="therapy-name">Rheumatology</div>
                                    <div class="therapy-count">3 indications</div>
                                </div>
                                
                                <div class="therapy-card" data-therapy="oncology" onclick="selectTherapy('oncology')">
                                    <div class="therapy-icon oncology-icon">🎗️</div>
                                    <div class="therapy-name">Oncology</div>
                                    <div class="therapy-count">5 indications</div>
                                </div>
                                
                                <div class="therapy-card" data-therapy="endocrinology" onclick="selectTherapy('endocrinology')">
                                    <div class="therapy-icon endocrinology-icon">⚖️</div>
                                    <div class="therapy-name">Endocrinology</div>
                                    <div class="therapy-count">3 indications</div>
                                </div>
                            </div>
                        </div>
                        <button class="pagination-btn next-btn" id="therapyNextBtn" onclick="changeTherapyPage(1)">
                            <span>›</span>
                        </button>
                    </div>
                </div>

                <!-- Indication Selection -->
                <div class="indication-section" id="indicationSection" style="display: none;">
                    <h4 style="color: #333; margin-bottom: 15px; font-size: 18px;">Select Indication</h4>
                    <div class="pagination-container">
                        <button class="pagination-btn prev-btn" id="indicationPrevBtn" onclick="changeIndicationPage(-1)">
                            <span>‹</span>
                        </button>
                        <div class="indication-pagination" id="indicationPagination">
                            <!-- Indication pages will be populated dynamically -->
                        </div>
                        <button class="pagination-btn next-btn" id="indicationNextBtn" onclick="changeIndicationPage(1)">
                            <span>›</span>
                        </button>
                    </div>
                </div>

                <button class="continue-btn" id="continueBtn1" onclick="showStep(2)" disabled>
                    💡
                </button>
            </div>

            <!-- Step 2: Enhanced Insight Report -->
            <div class="step" id="step2">
                <div class="report-layout">
                    <!-- Sidebar Navigation -->
                    <div class="sidebar-nav">
                        <a href="#" class="nav-item active" onclick="showSection('disease-overview')">
                            <span class="nav-icon">🧠</span>
                            <span>Disease Overview</span>
                        </a>
                        <a href="#" class="nav-item" onclick="showSection('market-evolution')">
                            <span class="nav-icon">📈</span>
                            <span>Market Evolution</span>
                        </a>
                        <a href="#" class="nav-item" onclick="showSection('patient-profile')">
                            <span class="nav-icon">👥</span>
                            <span>Patient Profile</span>
                        </a>
                        <a href="#" class="nav-item" onclick="showSection('unmet-need')">
                            <span class="nav-icon">🎯</span>
                            <span>Clinical Unmet Need</span>
                        </a>
                        <a href="#" class="nav-item" onclick="showSection('competitive-landscape')">
                            <span class="nav-icon">⚔️</span>
                            <span>Competitive Landscape</span>
                        </a>
                    </div>

                    <!-- Main Content Area -->
                    <div class="main-content-area">
                        <!-- Disease Overview Section (Default) -->
                        <div class="content-section active" id="disease-overview-section">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                                <h2 style="color: #333; font-size: 28px; font-weight: 700; margin: 0;">Schizophrenia Overview</h2>
                            </div>
                            
                            <!-- Disease Description -->
                            <div class="disease-description" style="margin-bottom: 20px; padding: 15px; background: rgba(74, 85, 104, 0.05); border-radius: 8px; border-left: 3px solid #4a5568;">
                                <p style="color: #555; font-size: 14px; line-height: 1.6; margin: 0;">
                                    Schizophrenia is a chronic and severe mental disorder affecting 1.1% of the global population. This complex neuropsychiatric condition fundamentally alters how individuals think, feel, and behave, representing one of the most debilitating mental health conditions worldwide with profound impacts on patients, families, and healthcare systems.
                                </p>
                            </div>
                            
                            <!-- What Happens in the Disease -->
                            <div style="margin-bottom: 18px;">
                                <h4 style="color: #333; font-size: 15px; font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                                    🧠 Disease Pathophysiology
                                </h4>
                                <p style="color: #333; font-size: 14px; line-height: 1.6; margin-bottom: 12px;">
                                    Schizophrenia disrupts brain function through complex neurochemical imbalances, primarily involving dopamine, glutamate, and GABA neurotransmitter systems. The disorder manifests through three distinct symptom clusters:
                                </p>
                                
                                <div style="display: grid; grid-template-columns: 1fr; gap: 10px; margin-bottom: 15px;">
                                    <div style="background: #f0f4f8; padding: 10px; border-radius: 6px; border-left: 3px solid #667eea;">
                                        <strong style="color: #333; font-size: 13px;">Positive Symptoms:</strong>
                                        <span style="color: #666; font-size: 13px; margin-left: 8px;">Delusions, hallucinations, disorganized thinking - symptoms "added" to normal experience</span>
                                    </div>
                                    <div style="background: #f0f8f0; padding: 10px; border-radius: 6px; border-left: 3px solid #38a169;">
                                        <strong style="color: #333; font-size: 13px;">Negative Symptoms:</strong>
                                        <span style="color: #666; font-size: 13px; margin-left: 8px;">Reduced emotional expression, avolition, social withdrawal - loss of normal functions</span>
                                    </div>
                                    <div style="background: #fef5e7; padding: 10px; border-radius: 6px; border-left: 3px solid #d69e2e;">
                                        <strong style="color: #333; font-size: 13px;">Cognitive Symptoms:</strong>
                                        <span style="color: #666; font-size: 13px; margin-left: 8px;">Impaired working memory, attention deficits, executive dysfunction</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Diagnosis -->
                            <div style="margin-bottom: 18px;">
                                <h4 style="color: #333; font-size: 15px; font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                                    🩺 Diagnostic Process
                                </h4>
                                <p style="color: #333; font-size: 14px; line-height: 1.6; margin-bottom: 10px;">
                                    Diagnosis relies on <strong>DSM-5 criteria</strong> requiring two or more psychotic symptoms for at least one month within a six-month period of functional decline. No single diagnostic test exists - clinicians use comprehensive psychiatric evaluations, medical history, and symptom observation. <strong>Average diagnostic delay: 2-5 years</strong> from symptom onset, contributing to poorer long-term outcomes.
                                </p>
                            </div>

                            <!-- Treatment Modalities -->
                            <div style="margin-bottom: 18px;">
                                <h4 style="color: #333; font-size: 15px; font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                                    💊 Treatment Landscape
                                </h4>
                                <p style="color: #333; font-size: 14px; line-height: 1.6; margin-bottom: 10px;">
                                    Current treatment is <strong>symptom-focused rather than curative</strong>, centered on antipsychotic medications that primarily target positive symptoms. Treatment includes first-generation (typical) and second-generation (atypical) antipsychotics, with emerging long-acting injectable formulations improving adherence. <strong>Significant gaps remain</strong> in addressing cognitive and negative symptoms, with only 25% of patients achieving adequate symptom control.
                                </p>
                            </div>

                            <!-- Companies Playing -->
                            <div style="margin-bottom: 20px;">
                                <h4 style="color: #333; font-size: 15px; font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                                    🏢 Market Players
                                </h4>
                                <p style="color: #333; font-size: 14px; line-height: 1.6; margin-bottom: 10px;">
                                    The market features <strong>45 active sponsors</strong> with 78 Phase II/III trials ongoing. Key players include Johnson & Johnson (Invega franchise), Otsuka/Lundbeck (Abilify), and Alkermes (Aristada). The landscape is shifting toward novel mechanisms targeting NMDA receptors, muscarinic agonists, and neuroinflammation pathways, with <strong>12 near-term launches</strong> expected to reshape treatment paradigms.
                                </p>
                            </div>
                            
                            <!-- Disease Characteristics KPIs -->
                            <div style="margin-bottom: 15px;">
                                <h4 style="color: #333; font-size: 15px; font-weight: 600; margin-bottom: 12px;">📊 Disease Characteristics</h4>
                            </div>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-card-value">1.1%</div>
                                    <div class="stat-card-label">Global Prevalence</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-card-value">24M</div>
                                    <div class="stat-card-label">Affected Individuals</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-card-value">Early 20s</div>
                                    <div class="stat-card-label">Typical Onset</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-card-value">2-5 yrs</div>
                                    <div class="stat-card-label">Diagnostic Delay</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-card-value">1.4:1</div>
                                    <div class="stat-card-label">Male:Female Ratio</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-card-value">15-20 yrs</div>
                                    <div class="stat-card-label">Life Expectancy Reduction</div>
                                </div>
                            </div>
                        </div>

                        <!-- Market Evolution Section -->
                        <div class="content-section" id="market-evolution-section">
                            <div style="margin-bottom: 25px;">
                                <h2 style="color: #333; font-size: 28px; font-weight: 700; margin: 0;">Market Evolution</h2>
                            </div>
                            
                            <!-- Market Summary -->
                            <div style="background: rgba(56, 161, 105, 0.1); border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #38a169;">
                                <p style="margin: 0; color: #2d3748; font-size: 15px; line-height: 1.6; font-weight: 500;">
                                    The schizophrenia market is experiencing steady growth driven by innovation in treatment approaches and increasing patient population awareness.
                                </p>
                            </div>

                            <!-- Current Market Status -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    📊 Current Market Status
                                </h3>
                                <div class="stats-grid">
                                    <div class="stat-card" style="border-left-color: #38a169;">
                                        <div class="stat-card-value">$7.6B</div>
                                        <div class="stat-card-label">Global Market Size (2024)</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #3182ce;">
                                        <div class="stat-card-value">4.8%</div>
                                        <div class="stat-card-label">Annual Growth Rate</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #805ad5;">
                                        <div class="stat-card-value">24M</div>
                                        <div class="stat-card-label">Patients Worldwide</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #d69e2e;">
                                        <div class="stat-card-value">$10.7B</div>
                                        <div class="stat-card-label">Projected 2030 Value</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Geographic Markets -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    🌍 Key Geographic Markets
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">🇺🇸 North America</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #38a169; margin-bottom: 5px;">45%</div>
                                        <div style="font-size: 13px; color: #718096;">Market share leader</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">🇪🇺 Europe</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #3182ce; margin-bottom: 5px;">32%</div>
                                        <div style="font-size: 13px; color: #718096;">Second largest market</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">🌏 Asia-Pacific</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #dd6b20; margin-bottom: 5px;">18%</div>
                                        <div style="font-size: 13px; color: #718096;">Fastest growing region</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">🌍 Rest of World</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #9f7aea; margin-bottom: 5px;">5%</div>
                                        <div style="font-size: 13px; color: #718096;">Emerging markets</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Market Drivers -->
                            <div style="margin-bottom: 20px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    🚀 Growth Drivers
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr; gap: 12px;">
                                    <div style="background: #f0f8f0; padding: 15px; border-radius: 8px; border-left: 4px solid #38a169;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Innovation in Treatment</div>
                                        <div style="font-size: 14px; color: #4a5568;">Novel mechanisms targeting negative and cognitive symptoms</div>
                                    </div>
                                    <div style="background: #f0f4f8; padding: 15px; border-radius: 8px; border-left: 4px solid #3182ce;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Long-Acting Formulations</div>
                                        <div style="font-size: 14px; color: #4a5568;">Injectable options improving patient adherence</div>
                                    </div>
                                    <div style="background: #fef5e7; padding: 15px; border-radius: 8px; border-left: 4px solid #d69e2e;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Increased Awareness</div>
                                        <div style="font-size: 14px; color: #4a5568;">Better diagnosis and treatment access globally</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Patient Profile Section -->
                        <div class="content-section" id="patient-profile-section">
                            <div style="margin-bottom: 25px;">
                                <h2 style="color: #333; font-size: 28px; font-weight: 700; margin: 0;">Patient Profile</h2>
                            </div>
                            
                            <!-- Patient Summary -->
                            <div style="background: rgba(49, 130, 206, 0.1); border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #3182ce;">
                                <p style="margin: 0; color: #2d3748; font-size: 15px; line-height: 1.6; font-weight: 500;">
                                    Schizophrenia patients face significant challenges with delayed diagnosis, high comorbidity rates, and substantial impact on quality of life and employment.
                                </p>
                            </div>

                            <!-- Patient Demographics -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    👥 Patient Demographics
                                </h3>
                                <div class="stats-grid">
                                    <div class="stat-card" style="border-left-color: #3182ce;">
                                        <div class="stat-card-value">1.4:1</div>
                                        <div class="stat-card-label">Male to Female Ratio</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #805ad5;">
                                        <div class="stat-card-value">22 years</div>
                                        <div class="stat-card-label">Average Age at Onset</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #38a169;">
                                        <div class="stat-card-value">65%</div>
                                        <div class="stat-card-label">Diagnosed by Age 25</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #d69e2e;">
                                        <div class="stat-card-value">2-5 years</div>
                                        <div class="stat-card-label">Diagnostic Delay</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Health Complications -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    🏥 Common Health Issues
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">😞 Depression</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #e53e3e; margin-bottom: 5px;">80%</div>
                                        <div style="font-size: 13px; color: #718096;">Co-occurring depression</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">🍷 Substance Abuse</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #dd6b20; margin-bottom: 5px;">50%</div>
                                        <div style="font-size: 13px; color: #718096;">Alcohol or drug abuse</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">🍯 Diabetes</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #38b2ac; margin-bottom: 5px;">25%</div>
                                        <div style="font-size: 13px; color: #718096;">Metabolic complications</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">❤️ Cardiovascular</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #9f7aea; margin-bottom: 5px;">35%</div>
                                        <div style="font-size: 13px; color: #718096;">Heart-related conditions</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Life Impact -->
                            <div style="margin-bottom: 20px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    📉 Impact on Daily Life
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr; gap: 12px;">
                                    <div style="background: #fef2f2; padding: 15px; border-radius: 8px; border-left: 4px solid #e53e3e;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Employment Challenges</div>
                                        <div style="font-size: 14px; color: #4a5568;">55% unemployment rate due to cognitive and social difficulties</div>
                                    </div>
                                    <div style="background: #fef5e7; padding: 15px; border-radius: 8px; border-left: 4px solid #d69e2e;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Reduced Life Expectancy</div>
                                        <div style="font-size: 14px; color: #4a5568;">15-20 years shorter lifespan compared to general population</div>
                                    </div>
                                    <div style="background: #f0f4f8; padding: 15px; border-radius: 8px; border-left: 4px solid #3182ce;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Quality of Life</div>
                                        <div style="font-size: 14px; color: #4a5568;">Significant impairment in social relationships and independent living</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Clinical Unmet Need Section -->
                        <div class="content-section" id="unmet-need-section">
                            <div style="margin-bottom: 25px;">
                                <h2 style="color: #333; font-size: 28px; font-weight: 700; margin: 0;">Clinical Unmet Need</h2>
                            </div>
                            
                            <!-- Summary -->
                            <div style="background: rgba(214, 158, 46, 0.1); border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #d69e2e;">
                                <p style="margin: 0; color: #2d3748; font-size: 15px; line-height: 1.6; font-weight: 500;">
                                    Current treatments leave many patients with inadequate symptom control, highlighting critical gaps in addressing cognitive symptoms and negative symptoms.
                                </p>
                            </div>

                            <!-- Treatment Effectiveness -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    💊 Current Treatment Effectiveness
                                </h3>
                                <div class="stats-grid">
                                    <div class="stat-card" style="border-left-color: #e53e3e;">
                                        <div class="stat-card-value">25%</div>
                                        <div class="stat-card-label">Achieve Good Control</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #dd6b20;">
                                        <div class="stat-card-value">60%</div>
                                        <div class="stat-card-label">Treatment-Resistant</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #805ad5;">
                                        <div class="stat-card-value">40%</div>
                                        <div class="stat-card-label">Non-Adherent</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #38b2ac;">
                                        <div class="stat-card-value">5.4/10</div>
                                        <div class="stat-card-label">Quality of Life</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Symptom Management Gaps -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    🎯 Major Treatment Gaps
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">🧠 Cognitive Symptoms</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #3182ce; margin-bottom: 5px;">85%</div>
                                        <div style="font-size: 13px; color: #718096;">Need cognitive enhancement</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">😶 Negative Symptoms</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #38a169; margin-bottom: 5px;">70%</div>
                                        <div style="font-size: 13px; color: #718096;">Experience symptom burden</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">⚡ Functional Issues</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #d69e2e; margin-bottom: 5px;">90%</div>
                                        <div style="font-size: 13px; color: #718096;">Have functional impairment</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">💼 Employment</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #9f7aea; margin-bottom: 5px;">55%</div>
                                        <div style="font-size: 13px; color: #718096;">Unemployment rate</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Innovation Opportunities -->
                            <div style="margin-bottom: 20px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    🚀 Innovation Opportunities
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr; gap: 12px;">
                                    <div style="background: #f0f4f8; padding: 15px; border-radius: 8px; border-left: 4px solid #3182ce;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Cognitive Enhancement Therapies</div>
                                        <div style="font-size: 14px; color: #4a5568;">Develop treatments targeting memory, attention, and executive function</div>
                                    </div>
                                    <div style="background: #f0f8f0; padding: 15px; border-radius: 8px; border-left: 4px solid #38a169;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Negative Symptom Management</div>
                                        <div style="font-size: 14px; color: #4a5568;">Address social withdrawal, reduced motivation, and emotional blunting</div>
                                    </div>
                                    <div style="background: #fef5e7; padding: 15px; border-radius: 8px; border-left: 4px solid #d69e2e;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Adherence Solutions</div>
                                        <div style="font-size: 14px; color: #4a5568;">Long-acting formulations and digital health tools for medication compliance</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Competitive Landscape Section -->
                        <div class="content-section" id="competitive-landscape-section">
                            <div style="margin-bottom: 25px;">
                                <h2 style="color: #333; font-size: 28px; font-weight: 700; margin: 0;">Competitive Landscape</h2>
                            </div>
                            
                            <!-- Summary -->
                            <div style="background: rgba(229, 62, 62, 0.1); border-radius: 8px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #e53e3e;">
                                <p style="margin: 0; color: #2d3748; font-size: 15px; line-height: 1.6; font-weight: 500;">
                                    The schizophrenia market is highly competitive with numerous established players, significant generic penetration, and intense pricing pressure.
                                </p>
                            </div>

                            <!-- Market Competition Level -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    ⚔️ Competition Intensity
                                </h3>
                                <div class="stats-grid">
                                    <div class="stat-card" style="border-left-color: #e53e3e;">
                                        <div class="stat-card-value">7.3/10</div>
                                        <div class="stat-card-label">Competition Risk Score</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #dd6b20;">
                                        <div class="stat-card-value">Red Ocean</div>
                                        <div class="stat-card-label">Market Classification</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #38b2ac;">
                                        <div class="stat-card-value">High</div>
                                        <div class="stat-card-label">Pricing Pressure</div>
                                    </div>
                                    <div class="stat-card" style="border-left-color: #9f7aea;">
                                        <div class="stat-card-value">65%</div>
                                        <div class="stat-card-label">Generic Market Share</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Key Market Players -->
                            <div style="margin-bottom: 25px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    🏢 Leading Companies
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr; gap: 12px; margin-bottom: 15px;">
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Johnson & Johnson</div>
                                                <div style="font-size: 13px; color: #718096;">Invega franchise - Leading LAI portfolio</div>
                                            </div>
                                            <div style="text-align: right;">
                                                <div style="font-size: 18px; font-weight: 700; color: #e53e3e;">#1</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Otsuka/Lundbeck</div>
                                                <div style="font-size: 13px; color: #718096;">Abilify family - Established brand</div>
                                            </div>
                                            <div style="text-align: right;">
                                                <div style="font-size: 18px; font-weight: 700; color: #dd6b20;">#2</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-weight: 600; color: #2d3748; margin-bottom: 5px;">Alkermes</div>
                                                <div style="font-size: 13px; color: #718096;">Aristada - Monthly injection</div>
                                            </div>
                                            <div style="text-align: right;">
                                                <div style="font-size: 18px; font-weight: 700; color: #38a169;">#3</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pipeline Activity -->
                            <div style="margin-bottom: 20px;">
                                <h3 style="color: #2d3748; font-size: 18px; font-weight: 600; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                                    🔬 Development Pipeline
                                </h3>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">🧪 Active Sponsors</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #e53e3e; margin-bottom: 5px;">45</div>
                                        <div style="font-size: 13px; color: #718096;">Companies in development</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">📋 Clinical Trials</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #3182ce; margin-bottom: 5px;">78</div>
                                        <div style="font-size: 13px; color: #718096;">Phase II/III studies</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">🚀 Near-term Launches</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #38a169; margin-bottom: 5px;">12</div>
                                        <div style="font-size: 13px; color: #718096;">Expected 2024-2026</div>
                                    </div>
                                    <div style="background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px;">
                                        <div style="font-weight: 600; color: #2d3748; margin-bottom: 8px;">🎯 Major Players</div>
                                        <div style="font-size: 24px; font-weight: 700; color: #805ad5; margin-bottom: 5px;">8</div>
                                        <div style="font-size: 13px; color: #718096;">Market dominance</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Removed -->
        </div>
    </div>

    <!-- KPI Modal -->
    <div id="kpiModal" class="kpi-modal">
        <div class="kpi-modal-content">
            <div class="kpi-modal-header">
                <h2 class="kpi-modal-title" id="kpiModalTitle">KPI Analysis</h2>
                <button class="kpi-modal-close" onclick="closeKPIModal()">&times;</button>
            </div>
            <div class="kpi-modal-body" id="kpiModalBody">
                <!-- KPI content will be populated dynamically -->
            </div>
        </div>
    </div>

    <!-- Theme Discovery Modal -->
    <div id="themeModal" class="theme-modal">
        <div class="theme-modal-content">
            <div class="theme-header">
                <h2 class="theme-title">
                    🔍 Theme Discovery - Schizophrenia Analysis
                </h2>
                <button class="theme-close" onclick="closeThemeDiscovery()">&times;</button>
            </div>
            <div class="theme-body">
                <div class="theme-intro">
                    <h3>📊 Intelligent Theme Extraction</h3>
                    <p>AI-powered analysis has extracted and organized key themes from schizophrenia research data into a hierarchical structure. Explore topics by expanding each category to discover deeper insights and patterns.</p>
                </div>

                <div class="theme-tree">
                    <!-- Level 1: Market & Commercial Themes -->
                    <div class="tree-level-1">
                        <div class="level-1-node" onclick="toggleLevel2(this, 'market-themes')">
                            <div class="level-1-content">
                                <div class="level-1-icon">💰</div>
                                <div class="level-1-text">Market & Commercial Landscape</div>
                            </div>
                            <div class="expand-indicator">▶</div>
                        </div>
                        <div class="tree-level-2" id="market-themes">
                            <div class="level-2-node" onclick="toggleLevel3(this, 'market-size')">
                                <div class="level-2-content">
                                    <div class="level-2-icon">📈</div>
                                    <div class="level-2-text">Market Dynamics & Growth</div>
                                </div>
                                <div class="expand-indicator">▶</div>
                            </div>
                            <div class="tree-level-3" id="market-size">
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">$7.6B current market size with 4.8% CAGR growth trajectory</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">Regional distribution: North America (45%), Europe (32%), APAC (18%)</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">Projected $10.7B market value by 2030 driven by novel mechanisms</div>
                                </div>
                            </div>

                            <div class="level-2-node" onclick="toggleLevel3(this, 'competitive-intensity')">
                                <div class="level-2-content">
                                    <div class="level-2-icon">⚔️</div>
                                    <div class="level-2-text">Competitive Environment</div>
                                </div>
                                <div class="expand-indicator">▶</div>
                            </div>
                            <div class="tree-level-3" id="competitive-intensity">
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">Red ocean market with 45 active sponsors and 7.3/10 risk score</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">Major players: J&J (Invega), Otsuka/Lundbeck (Abilify), Alkermes</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">78 Phase II/III trials ongoing with 12 near-term launches expected</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Level 1: Clinical & Patient Themes -->
                    <div class="tree-level-1">
                        <div class="level-1-node" onclick="toggleLevel2(this, 'clinical-themes')">
                            <div class="level-1-content">
                                <div class="level-1-icon">🏥</div>
                                <div class="level-1-text">Clinical & Patient Insights</div>
                            </div>
                            <div class="expand-indicator">▶</div>
                        </div>
                        <div class="tree-level-2" id="clinical-themes">
                            <div class="level-2-node" onclick="toggleLevel3(this, 'patient-demographics')">
                                <div class="level-2-content">
                                    <div class="level-2-icon">👥</div>
                                    <div class="level-2-text">Patient Demographics & Profile</div>
                                </div>
                                <div class="expand-indicator">▶</div>
                            </div>
                            <div class="tree-level-3" id="patient-demographics">
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">1.4:1 male predominance with typical onset in early 20s</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">High comorbidity burden: 80% depression, 50% substance abuse</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">2-5 year diagnostic delay with 65% diagnosed by age 25</div>
                                </div>
                            </div>

                            <div class="level-2-node" onclick="toggleLevel3(this, 'disease-burden')">
                                <div class="level-2-content">
                                    <div class="level-2-icon">⚠️</div>
                                    <div class="level-2-text">Disease Burden & Impact</div>
                                </div>
                                <div class="expand-indicator">▶</div>
                            </div>
                            <div class="tree-level-3" id="disease-burden">
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">15-20 years reduced life expectancy with 55% unemployment rate</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">Global prevalence of 1.1% affecting 24 million individuals worldwide</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">Quality of life score 5.4/10 with 90% functional impairment</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Level 1: Treatment & Innovation Themes -->
                    <div class="tree-level-1">
                        <div class="level-1-node" onclick="toggleLevel2(this, 'treatment-themes')">
                            <div class="level-1-content">
                                <div class="level-1-icon">💊</div>
                                <div class="level-1-text">Treatment & Innovation Landscape</div>
                            </div>
                            <div class="expand-indicator">▶</div>
                        </div>
                        <div class="tree-level-2" id="treatment-themes">
                            <div class="level-2-node" onclick="toggleLevel3(this, 'unmet-needs')">
                                <div class="level-2-content">
                                    <div class="level-2-icon">🎯</div>
                                    <div class="level-2-text">Unmet Medical Needs</div>
                                </div>
                                <div class="expand-indicator">▶</div>
                            </div>
                            <div class="tree-level-3" id="unmet-needs">
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">60% treatment-resistant cases with only 25% achieving adequate control</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">85% require cognitive enhancement but limited options available</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">70% negative symptom burden with few targeted therapies</div>
                                </div>
                            </div>

                            <div class="level-2-node" onclick="toggleLevel3(this, 'innovation-opportunities')">
                                <div class="level-2-content">
                                    <div class="level-2-icon">🚀</div>
                                    <div class="level-2-text">Innovation & Research Directions</div>
                                </div>
                                <div class="expand-indicator">▶</div>
                            </div>
                            <div class="tree-level-3" id="innovation-opportunities">
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">Novel mechanisms: NMDA receptors, muscarinic agonists, neuroinflammation</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">Long-acting injectable formulations improving adherence rates</div>
                                </div>
                                <div class="level-3-node">
                                    <div class="level-3-icon">•</div>
                                    <div class="level-3-text">Digital therapeutics and AI-assisted treatment optimization</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Theme Statistics -->
                <div class="theme-stats">
                    <div class="stat-item">
                        <div class="stat-value">156</div>
                        <div class="stat-label">Themes Extracted</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">3</div>
                        <div class="stat-label">Hierarchy Levels</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">92%</div>
                        <div class="stat-label">Coverage Confidence</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">2.4k</div>
                        <div class="stat-label">Data Points</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chatbot Modal -->
    <div id="chatbotModal" class="chatbot-modal">
        <div class="chatbot-modal-content">
            <div class="chatbot-header">
                <h2 class="chatbot-title">
                    🤖 Disease Analyzer AI Assistant
                </h2>
                <button class="chatbot-close" onclick="closeChatbot()">&times;</button>
            </div>
            <div class="chatbot-body">
                <!-- Suggested Questions -->
                <div class="suggested-questions">
                    <h4>💡 Suggested Questions:</h4>
                    <span class="suggestion-chip" onclick="askSuggestion('What are the key unmet needs in schizophrenia?')">Key unmet needs</span>
                    <span class="suggestion-chip" onclick="askSuggestion('How large is the schizophrenia market?')">Market size</span>
                    <span class="suggestion-chip" onclick="askSuggestion('Who are the main competitors?')">Main competitors</span>
                    <span class="suggestion-chip" onclick="askSuggestion('What are the treatment challenges?')">Treatment challenges</span>
                </div>

                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages">
                    <!-- Welcome message -->
                    <div class="message bot">
                        <div class="message-avatar">🤖</div>
                        <div>
                            <div class="message-content">
                                Hello! I'm your Disease Analyzer AI assistant. I can help you explore insights about schizophrenia market dynamics, patient profiles, competitive landscape, and unmet needs. What would you like to know?
                            </div>
                            <div class="message-time">Just now</div>
                        </div>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div class="typing-indicator" id="typingIndicator">
                    <div class="message bot">
                        <div class="message-avatar">🤖</div>
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="chat-input-area">
                    <div class="chat-input-container">
                        <input type="text" class="chat-input" id="chatInput" placeholder="Ask me anything about schizophrenia analysis..." onkeypress="handleChatKeyPress(event)">
                        <button class="chat-send-btn" onclick="sendMessage()">➤</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedTherapy = null;
        let selectedIndication = null;
        let selectedIndicationName = null;
        let selectedCategory = null;
        let currentTherapyPage = 1;
        let currentIndicationPage = 1;
        let totalIndicationPages = 1;

        function selectTherapy(therapy) {
            selectedTherapy = therapy;
            
            // Update therapy card selection
            document.querySelectorAll('.therapy-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-therapy="${therapy}"]`).classList.add('selected');
            
            // Update progress indicators
            document.getElementById('therapyCircle').classList.add('active');
            
            // Show indication section with animation
            const indicationSection = document.getElementById('indicationSection');
            indicationSection.style.display = 'block';
            setTimeout(() => {
                indicationSection.classList.add('show');
            }, 100);
            
            // Populate indications based on therapy
            populateIndications(therapy);
            
            // Clear indication selection
            selectedIndication = null;
            document.getElementById('indicationCircle').classList.remove('active');
            updateContinueButton();
        }

        function changeTherapyPage(direction) {
            const currentPage = document.getElementById(`therapyPage${currentTherapyPage}`);
            const newPageNumber = currentTherapyPage + direction;
            
            if (newPageNumber < 1 || newPageNumber > 2) return;
            
            const newPage = document.getElementById(`therapyPage${newPageNumber}`);
            
            // Animate out current page
            currentPage.classList.remove('active');
            if (direction > 0) {
                currentPage.classList.add('prev');
            } else {
                currentPage.style.transform = 'translateX(100%)';
            }
            
            // Animate in new page
            setTimeout(() => {
                if (direction > 0) {
                    newPage.style.transform = 'translateX(100%)';
                } else {
                    newPage.classList.add('prev');
                }
                
                setTimeout(() => {
                    newPage.classList.add('active');
                    newPage.classList.remove('prev');
                    newPage.style.transform = '';
                }, 50);
            }, 200);
            
            currentTherapyPage = newPageNumber;
            updateTherapyPaginationButtons();
        }

        function updateTherapyPaginationButtons() {
            document.getElementById('therapyPrevBtn').disabled = currentTherapyPage === 1;
            document.getElementById('therapyNextBtn').disabled = currentTherapyPage === 2;
        }

        function changeIndicationPage(direction) {
            const currentPage = document.getElementById(`indicationPage${currentIndicationPage}`);
            const newPageNumber = currentIndicationPage + direction;
            
            if (newPageNumber < 1 || newPageNumber > totalIndicationPages) return;
            
            const newPage = document.getElementById(`indicationPage${newPageNumber}`);
            
            // Animate out current page
            currentPage.classList.remove('active');
            if (direction > 0) {
                currentPage.classList.add('prev');
            } else {
                currentPage.style.transform = 'translateX(100%)';
            }
            
            // Animate in new page
            setTimeout(() => {
                if (direction > 0) {
                    newPage.style.transform = 'translateX(100%)';
                } else {
                    newPage.classList.add('prev');
                }
                
                setTimeout(() => {
                    newPage.classList.add('active');
                    newPage.classList.remove('prev');
                    newPage.style.transform = '';
                }, 50);
            }, 200);
            
            currentIndicationPage = newPageNumber;
            updateIndicationPaginationButtons();
        }

        function updateIndicationPaginationButtons() {
            document.getElementById('indicationPrevBtn').disabled = currentIndicationPage === 1;
            document.getElementById('indicationNextBtn').disabled = currentIndicationPage === totalIndicationPages;
        }

        function populateIndications(therapy) {
            const indicationPagination = document.getElementById('indicationPagination');
            currentIndicationPage = 1; // Reset to first page
            
            const indications = {
                'psychiatry': [
                    { id: 'adhd', name: 'ADHD', desc: 'Attention Deficit Hyperactivity Disorder', icon: '🎯' },
                    { id: 'schizophrenia', name: 'Schizophrenia', desc: 'Chronic brain disorder', icon: '🧠' },
                    { id: 'depression', name: 'Depression', desc: 'Major depressive disorder', icon: '😔' },
                    { id: 'bipolar', name: 'Bipolar Disorder', desc: 'Mood disorder', icon: '🔄' }
                ],
                'nephrology': [
                    { id: 'nephropathy', name: 'Nephropathy', desc: 'Kidney disease', icon: '🫘' },
                    { id: 'adpkd', name: 'ADPKD', desc: 'Autosomal dominant polycystic kidney disease', icon: '🔗' },
                    { id: 'ckd', name: 'CKD', desc: 'Chronic kidney disease', icon: '⚠️' },
                    { id: 'aki', name: 'AKI', desc: 'Acute kidney injury', icon: '🚨' }
                ],
                'dermatology': [
                    { id: 'ad', name: 'Atopic Dermatitis', desc: 'Chronic inflammatory skin condition', icon: '🌡️' },
                    { id: 'psoriasis', name: 'Psoriasis', desc: 'Autoimmune skin disorder', icon: '🩹' },
                    { id: 'eczema', name: 'Eczema', desc: 'Inflammatory skin condition', icon: '💊' }
                ],
                'rheumatology': [
                    { id: 'ra', name: 'Rheumatoid Arthritis', desc: 'Autoimmune joint disorder', icon: '🦴' },
                    { id: 'osteoarthritis', name: 'Osteoarthritis', desc: 'Degenerative joint disease', icon: '⚡' },
                    { id: 'lupus', name: 'Lupus', desc: 'Systemic autoimmune disease', icon: '🔥' }
                ],
                'oncology': [
                    { id: 'lung-cancer', name: 'Lung Cancer', desc: 'Non-small cell lung cancer', icon: '🫁' },
                    { id: 'breast-cancer', name: 'Breast Cancer', desc: 'Hormone receptor positive', icon: '🎗️' },
                    { id: 'colorectal', name: 'Colorectal Cancer', desc: 'Metastatic colorectal cancer', icon: '🔬' },
                    { id: 'leukemia', name: 'Leukemia', desc: 'Acute lymphoblastic leukemia', icon: '🩸' },
                    { id: 'melanoma', name: 'Melanoma', desc: 'Advanced melanoma', icon: '☀️' }
                ],
                'endocrinology': [
                    { id: 'diabetes-t1', name: 'Type 1 Diabetes', desc: 'Insulin-dependent diabetes', icon: '💉' },
                    { id: 'diabetes-t2', name: 'Type 2 Diabetes', desc: 'Non-insulin dependent diabetes', icon: '📊' },
                    { id: 'thyroid', name: 'Thyroid Disorders', desc: 'Hyperthyroidism and hypothyroidism', icon: '⚖️' }
                ]
            };

            const therapyIndications = indications[therapy] || [];
            const indicationsPerPage = 3;
            totalIndicationPages = Math.ceil(therapyIndications.length / indicationsPerPage);
            
            // Clear existing pages
            indicationPagination.innerHTML = '';
            
            // Create pages
            for (let page = 1; page <= totalIndicationPages; page++) {
                const startIndex = (page - 1) * indicationsPerPage;
                const endIndex = startIndex + indicationsPerPage;
                const pageIndications = therapyIndications.slice(startIndex, endIndex);
                
                const pageDiv = document.createElement('div');
                pageDiv.className = `indication-page ${page === 1 ? 'active' : ''}`;
                pageDiv.id = `indicationPage${page}`;
                
                pageDiv.innerHTML = pageIndications.map(indication => `
                    <div class="indication-card" data-indication="${indication.id}" onclick="selectIndication('${indication.id}', '${indication.name}')">
                        <div class="indication-icon ${indication.id}-icon">${indication.icon}</div>
                        <div class="indication-name">${indication.name}</div>
                        <div class="indication-desc">${indication.desc}</div>
                    </div>
                `).join('');
                
                indicationPagination.appendChild(pageDiv);
            }
            
            updateIndicationPaginationButtons();
        }

        function selectIndication(indicationId, indicationName) {
            selectedIndication = indicationId;
            selectedIndicationName = indicationName;
            
            // Update indication card selection
            document.querySelectorAll('.indication-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-indication="${indicationId}"]`).classList.add('selected');
            
            // Update progress indicators
            document.getElementById('indicationCircle').classList.add('active');
            document.querySelector('.progress-line').classList.add('active');
            
            updateContinueButton();
        }

        function updateContinueButton() {
            const btn = document.getElementById('continueBtn1');
            btn.disabled = !(selectedTherapy && selectedIndication);
        }



        function showStep(stepNumber) {
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.getElementById(`step${stepNumber}`).classList.add('active');
            document.querySelectorAll('.nav-tab')[stepNumber - 1].classList.add('active');
            
            // Update report content when showing step 2
            if (stepNumber === 2 && selectedTherapy && selectedIndication) {
                updateReportContent();
                enableHeaderButtons();
            } else {
                // Disable buttons if not on step 2 or selections incomplete
                disableHeaderButtons();
            }
        }

        function disableHeaderButtons() {
            const themeBtn = document.getElementById('themeDiscoveryBtn');
            const chatBtn = document.getElementById('chatbotBtn');
            
            // Disable Theme Discovery button
            themeBtn.disabled = true;
            themeBtn.style.opacity = '0.5';
            themeBtn.style.cursor = 'not-allowed';
            
            // Disable Chatbot button
            chatBtn.disabled = true;
            chatBtn.style.opacity = '0.5';
            chatBtn.style.cursor = 'not-allowed';
        }

        function enableHeaderButtons() {
            const themeBtn = document.getElementById('themeDiscoveryBtn');
            const chatBtn = document.getElementById('chatbotBtn');
            
            // Enable Theme Discovery button
            themeBtn.disabled = false;
            themeBtn.style.opacity = '1';
            themeBtn.style.cursor = 'pointer';
            
            // Enable Chatbot button
            chatBtn.disabled = false;
            chatBtn.style.opacity = '1';
            chatBtn.style.cursor = 'pointer';
        }

        function updateReportContent() {
            const reportTitle = document.getElementById('reportTitle');
            const diseaseSummary = document.getElementById('diseaseSummary');
            const diseaseDescriptionText = document.getElementById('diseaseDescriptionText');
            const diseaseTitle = document.getElementById('diseaseTitle');

            if (reportTitle && diseaseSummary && diseaseDescriptionText && diseaseTitle) {
                // Always show Schizophrenia report regardless of selection
                const indicationName = 'Schizophrenia';
                
                reportTitle.textContent = indicationName;
                diseaseTitle.textContent = indicationName;
                
                // Always use Schizophrenia description
                const schizophreniaDescription = 'Schizophrenia is a chronic and severe mental disorder that affects how a person thinks, feels, and behaves. Characterized by positive symptoms (delusions, hallucinations), negative symptoms (reduced emotional expression, avolition), and cognitive symptoms (impaired working memory, attention deficits), schizophrenia significantly impairs daily functioning across multiple domains. The disorder typically emerges in late adolescence or early adulthood and requires lifelong management. Current treatments focus primarily on positive symptom control through antipsychotic medications, with significant unmet needs remaining in cognitive enhancement, negative symptom management, and functional recovery. The condition affects approximately 1% of the global population and represents one of the leading causes of disability worldwide, imposing substantial burden on patients, families, and healthcare systems.';
                
                diseaseSummary.textContent = schizophreniaDescription;
                diseaseDescriptionText.textContent = schizophreniaDescription;
            }
        }

        function populateKPIDetails(category) {
            const categoryTitles = {
                'disease-overview': 'Disease Overview Analysis',
                'market-evolution': 'Market Evolution Analysis', 
                'patient-insights': 'Patient Insights Analysis',
                'unmet-need': 'Unmet Need Assessment',
                'competitive-intensity': 'Competitive Intensity Analysis'
            };

            document.getElementById('selectedCategoryTitle').textContent = categoryTitles[category];

            const kpiDetails = document.getElementById('kpiDetails');
            
            if (category === 'disease-overview') {
                kpiDetails.innerHTML = `
                    <h3 style="color: #333; margin-bottom: 20px;">Disease Overview - ADHD Analysis</h3>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-title">Disease Clinical Description</div>
                            <div class="kpi-value">Complete</div>
                            <div class="kpi-trend">✅ Comprehensive profile</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Detailed clinical description and pathophysiology available
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Diagnostic Process</div>
                            <div class="kpi-value">DSM-5</div>
                            <div class="kpi-trend">📋 Standardized criteria</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Well-established diagnostic criteria and assessment tools
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Treatment Modalities</div>
                            <div class="kpi-value">Multiple</div>
                            <div class="kpi-trend">💊 Diverse options</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Stimulants, non-stimulants, and behavioral interventions
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Disease Severity Spectrum</div>
                            <div class="kpi-value">Mild-Severe</div>
                            <div class="kpi-trend">📊 Broad range</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Comprehensive severity classification system exists
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <div class="summary-card">
                            <div class="summary-title">Disease Overview Summary</div>
                            <div class="summary-text">
                                ADHD is a well-characterized neurodevelopmental disorder with established DSM-5 diagnostic criteria. The disease presents across a spectrum from mild to severe, affecting attention, hyperactivity, and impulsivity. Multiple treatment modalities exist including stimulant medications, non-stimulant alternatives, and behavioral interventions, providing comprehensive therapeutic options for patients.
                            </div>
                        </div>
                    </div>
                `;
            } else if (category === 'market-evolution') {
                kpiDetails.innerHTML = `
                    <h3 style="color: #333; margin-bottom: 20px;">Market Evolution - ADHD Analysis</h3>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-title">Epidemiology Trends</div>
                            <div class="kpi-value">8.4%</div>
                            <div class="kpi-trend">↗️ Growing prevalence</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Increasing incidence and prevalence rates globally
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Market Size & Growth</div>
                            <div class="kpi-value">$17.9B</div>
                            <div class="kpi-trend">📈 7.3% CAGR</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Global market size with strong growth trajectory
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Current Sales Leaders</div>
                            <div class="kpi-value">Top 3</div>
                            <div class="kpi-trend">💰 $8.2B combined</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Market share across key therapeutic classes
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Future Projections</div>
                            <div class="kpi-value">$24.9B</div>
                            <div class="kpi-trend">🎯 2030 forecast</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Projected market size by 2030 across brands and manufacturers
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <div class="summary-card">
                            <div class="summary-title">Market Evolution Summary</div>
                            <div class="summary-text">
                                The ADHD market shows robust growth with 8.4% prevalence and strong commercial expansion (7.3% CAGR). Current market size of $17.9B is dominated by established stimulant therapies, with projections reaching $24.9B by 2030. Increasing adult diagnosis and recognition of ADHD across demographics drives sustained market evolution and opportunity.
                            </div>
                        </div>
                    </div>
                `;
            } else if (category === 'patient-insights') {
                kpiDetails.innerHTML = `
                    <h3 style="color: #333; margin-bottom: 20px;">Patient Insights - ADHD Analysis</h3>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-title">Patient Demographics</div>
                            <div class="kpi-value">60:40</div>
                            <div class="kpi-trend">👦👧 Male:Female ratio</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Age and gender distribution across patient population
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Key Comorbidities</div>
                            <div class="kpi-value">65%</div>
                            <div class="kpi-trend">🧠 Anxiety/Depression</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Percentage with significant comorbid conditions
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Treatment Preferences</div>
                            <div class="kpi-value">85%</div>
                            <div class="kpi-trend">💊 Oral medications</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Share across treatment modalities by brand and class
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Patient Journey Length</div>
                            <div class="kpi-value">18 mo</div>
                            <div class="kpi-trend">⏱️ Diagnosis to optimal treatment</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Average time from initial symptoms to stable therapy
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <div class="summary-card">
                            <div class="summary-title">Patient Insights Summary</div>
                            <div class="summary-text">
                                ADHD patients show male predominance (60:40) with high comorbidity burden, particularly anxiety and depression (65%). Strong preference for oral medications (85%) reflects convenience needs. The patient journey averages 18 months from diagnosis to optimal treatment, highlighting opportunities for faster therapeutic optimization and improved patient experience.
                            </div>
                        </div>
                    </div>
                `;
            } else if (category === 'unmet-need') {
                kpiDetails.innerHTML = `
                    <h3 style="color: #333; margin-bottom: 20px;">Unmet Need - ADHD Analysis</h3>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-title">Clinical Outcome Gaps</div>
                            <div class="kpi-value">35%</div>
                            <div class="kpi-trend">⚠️ Suboptimal response</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Patients with under/delayed diagnosis, survival, safety concerns
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Patient Experience Challenges</div>
                            <div class="kpi-value">5.4/10</div>
                            <div class="kpi-trend">😞 Below acceptable</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Quality of life impact including pain, AE management, administration
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">KOL Standard of Care Gaps</div>
                            <div class="kpi-value">42%</div>
                            <div class="kpi-trend">📋 Deviation from guidelines</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                KOL opinions and deviation from standard treatment protocols
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Treatment Discontinuation</div>
                            <div class="kpi-value">22%</div>
                            <div class="kpi-trend">⚠️ High discontinuation</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Percentage of patients discontinuing therapy within 12 months
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <div class="summary-card">
                            <div class="summary-title">Unmet Need Summary</div>
                            <div class="summary-text">
                                Significant unmet needs exist with 35% of patients experiencing suboptimal clinical outcomes and poor quality of life scores (5.4/10). KOL surveys indicate 42% deviation from standard care protocols, while high discontinuation rates (22%) reflect tolerability and efficacy challenges. These gaps represent clear opportunities for improved therapeutic approaches and patient management strategies.
                            </div>
                        </div>
                    </div>
                `;
            } else if (category === 'competitive-intensity') {
                kpiDetails.innerHTML = `
                    <h3 style="color: #333; margin-bottom: 20px;">Competitive Intensity - ADHD Analysis</h3>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-title">Market Crowding</div>
                            <div class="kpi-value">Red Ocean</div>
                            <div class="kpi-trend">🌊 Highly competitive</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Blue vs red ocean competitive landscape assessment
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Pipeline Activity</div>
                            <div class="kpi-value">28</div>
                            <div class="kpi-trend">⚠️ High activity</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Number of unique sponsors with Phase II+ assets in disease area and class
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Pricing Pressure</div>
                            <div class="kpi-value">High</div>
                            <div class="kpi-trend">💰 Cost constraints</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Pricing and access hurdles across major markets
                            </p>
                        </div>
                        
                        <div class="kpi-card">
                            <div class="kpi-title">Competitive Risk Score</div>
                            <div class="kpi-value">7.3/10</div>
                            <div class="kpi-trend">🔴 High risk</div>
                            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                                Overall risk assessment for market entry and success
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <div class="summary-card">
                            <div class="summary-title">Competitive Intensity Summary</div>
                            <div class="summary-text">
                                ADHD represents a red ocean market with intense competition from 28 active pipeline sponsors. High pricing pressure and access hurdles create additional barriers to entry. The overall competitive risk score of 7.3/10 indicates significant challenges for new entrants, requiring strong differentiation and strategic positioning to achieve commercial success.
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        function toggleInsightCard(card) {
            const expandBtn = card.querySelector('.expand-btn');
            const details = card.querySelector('.insight-details');
            
            if (details.classList.contains('show')) {
                details.classList.remove('show');
                expandBtn.textContent = '📖 Expand';
                expandBtn.classList.remove('expanded');
                card.classList.remove('expanded');
            } else {
                details.classList.add('show');
                expandBtn.textContent = '📚 Collapse';
                expandBtn.classList.add('expanded');
                card.classList.add('expanded');
            }
        }

        function toggleBookmark(event, btn) {
            event.stopPropagation();
            
            if (btn.classList.contains('bookmarked')) {
                btn.classList.remove('bookmarked');
                btn.innerHTML = '⭐ Bookmark';
            } else {
                btn.classList.add('bookmarked');
                btn.innerHTML = '🌟 Bookmarked';
            }
        }

        function openKPIPopup(event, dimension) {
            event.stopPropagation();
            
            const modal = document.getElementById('kpiModal');
            const title = document.getElementById('kpiModalTitle');
            const body = document.getElementById('kpiModalBody');
            
            // Set title and content based on dimension
            const kpiData = getKPIData(dimension);
            title.textContent = kpiData.title;
            body.innerHTML = kpiData.content;
            
            // Show modal
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeKPIModal() {
            const modal = document.getElementById('kpiModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function getKPIData(dimension) {
            const kpiData = {
                'market-evolution': {
                    title: '📈 Market Evolution KPIs',
                    content: `
                        <div class="kpi-description">
                            <p>Comprehensive analysis of schizophrenia market dynamics, growth patterns, and future projections across global markets.</p>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">💰 Market Size & Growth</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #38a169;">
                                    <div class="kpi-value">$7.6B</div>
                                    <div class="kpi-label">Current Market Size (2024)</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #3182ce;">
                                    <div class="kpi-value">4.8%</div>
                                    <div class="kpi-label">CAGR (2023-2030)</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #805ad5;">
                                    <div class="kpi-value">$10.7B</div>
                                    <div class="kpi-label">2030 Projected Size</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #d69e2e;">
                                    <div class="kpi-value">41%</div>
                                    <div class="kpi-label">Growth Over 7 Years</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">🌍 Regional Distribution</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #e53e3e;">
                                    <div class="kpi-value">45%</div>
                                    <div class="kpi-label">North America Share</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38b2ac;">
                                    <div class="kpi-value">32%</div>
                                    <div class="kpi-label">Europe Share</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #dd6b20;">
                                    <div class="kpi-value">18%</div>
                                    <div class="kpi-label">Asia-Pacific Share</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #9f7aea;">
                                    <div class="kpi-value">5%</div>
                                    <div class="kpi-label">Rest of World</div>
                                </div>
                            </div>
                        </div>
                    `
                },
                'patient-profile': {
                    title: '🩺 Patient Profile KPIs',
                    content: `
                        <div class="kpi-description">
                            <p>Detailed demographic and clinical characteristics of schizophrenia patients, including comorbidities and treatment patterns.</p>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">👥 Demographics</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #3182ce;">
                                    <div class="kpi-value">1.4:1</div>
                                    <div class="kpi-label">Male to Female Ratio</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #805ad5;">
                                    <div class="kpi-value">22 yrs</div>
                                    <div class="kpi-label">Average Age of Onset</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38a169;">
                                    <div class="kpi-value">65%</div>
                                    <div class="kpi-label">Diagnosed by Age 25</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #d69e2e;">
                                    <div class="kpi-value">2-5 yrs</div>
                                    <div class="kpi-label">Diagnostic Delay</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">🏥 Comorbidities & Impact</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #e53e3e;">
                                    <div class="kpi-value">80%</div>
                                    <div class="kpi-label">Have Depression</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #dd6b20;">
                                    <div class="kpi-value">50%</div>
                                    <div class="kpi-label">Substance Abuse Rate</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38b2ac;">
                                    <div class="kpi-value">25%</div>
                                    <div class="kpi-label">Diabetes Prevalence</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #9f7aea;">
                                    <div class="kpi-value">15-20 yrs</div>
                                    <div class="kpi-label">Reduced Life Expectancy</div>
                                </div>
                            </div>
                        </div>
                    `
                },
                'unmet-need': {
                    title: '🎯 Clinical Unmet Need KPIs',
                    content: `
                        <div class="kpi-description">
                            <p>Assessment of treatment gaps, response rates, and areas where current therapies fall short of patient needs.</p>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">⚠️ Treatment Response</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #e53e3e;">
                                    <div class="kpi-value">60%</div>
                                    <div class="kpi-label">Treatment-Resistant Cases</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #dd6b20;">
                                    <div class="kpi-value">40%</div>
                                    <div class="kpi-label">Medication Non-Adherence</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #805ad5;">
                                    <div class="kpi-value">25%</div>
                                    <div class="kpi-label">Adequate Symptom Control</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38b2ac;">
                                    <div class="kpi-value">5.4/10</div>
                                    <div class="kpi-label">Quality of Life Score</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">🎯 Opportunity Areas</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #3182ce;">
                                    <div class="kpi-value">85%</div>
                                    <div class="kpi-label">Need Cognitive Enhancement</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38a169;">
                                    <div class="kpi-value">70%</div>
                                    <div class="kpi-label">Negative Symptom Burden</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #d69e2e;">
                                    <div class="kpi-value">90%</div>
                                    <div class="kpi-label">Functional Impairment</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #9f7aea;">
                                    <div class="kpi-value">55%</div>
                                    <div class="kpi-label">Unemployment Rate</div>
                                </div>
                            </div>
                        </div>
                    `
                },
                'competitive-landscape': {
                    title: '⚔️ Competitive Landscape KPIs',
                    content: `
                        <div class="kpi-description">
                            <p>Analysis of competitive intensity, market participants, and strategic positioning within the schizophrenia therapeutic space.</p>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">🏢 Market Players</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #e53e3e;">
                                    <div class="kpi-value">45</div>
                                    <div class="kpi-label">Active Sponsors</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #3182ce;">
                                    <div class="kpi-value">78</div>
                                    <div class="kpi-label">Phase II/III Trials</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38a169;">
                                    <div class="kpi-value">12</div>
                                    <div class="kpi-label">Near-term Launches</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #805ad5;">
                                    <div class="kpi-value">8</div>
                                    <div class="kpi-label">Major Players</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kpi-section">
                            <div class="kpi-section-title">📊 Competitive Intensity</div>
                            <div class="kpi-grid">
                                <div class="kpi-item" style="border-left-color: #e53e3e;">
                                    <div class="kpi-value">7.3/10</div>
                                    <div class="kpi-label">Risk Score</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #dd6b20;">
                                    <div class="kpi-value">Red Ocean</div>
                                    <div class="kpi-label">Market Status</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #38b2ac;">
                                    <div class="kpi-value">High</div>
                                    <div class="kpi-label">Pricing Pressure</div>
                                </div>
                                <div class="kpi-item" style="border-left-color: #9f7aea;">
                                    <div class="kpi-value">65%</div>
                                    <div class="kpi-label">Generic Share</div>
                                </div>
                            </div>
                        </div>
                    `
                }
            };
            
            return kpiData[dimension] || { title: 'KPIs', content: '<p>No data available</p>' };
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('kpiModal');
            if (event.target === modal) {
                closeKPIModal();
            }
        }

        function showSection(sectionName) {
            // Hide all content sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Remove active state from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionName + '-section').classList.add('active');
            
            // Set active state on clicked nav item
            event.target.classList.add('active');
        }

        function openChatbot() {
            const chatBtn = document.getElementById('chatbotBtn');
            if (chatBtn.disabled) {
                return; // Don't open if button is disabled
            }
            
            const modal = document.getElementById('chatbotModal');
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // Focus on input
            setTimeout(() => {
                document.getElementById('chatInput').focus();
            }, 300);
        }

        function closeChatbot() {
            const modal = document.getElementById('chatbotModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (message === '') return;
            
            // Add user message
            addMessage(message, 'user');
            
            // Clear input
            input.value = '';
            
            // Show typing indicator
            showTypingIndicator();
            
            // Simulate AI response after delay
            setTimeout(() => {
                hideTypingIndicator();
                const response = generateAIResponse(message);
                addMessage(response, 'bot');
            }, 1500);
        }

        function addMessage(content, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${sender === 'bot' ? '🤖' : '👤'}</div>
                <div>
                    <div class="message-content">${content}</div>
                    <div class="message-time">${time}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.style.display = 'block';
            
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.style.display = 'none';
        }

                 function generateAIResponse(question) {
             const responses = {
                 'what are the key unmet needs in schizophrenia?': `Based on our analysis, the key unmet needs in schizophrenia include:<br><br>🎯 <strong>Treatment Response</strong>: 60% of patients are treatment-resistant, and only 25% achieve adequate symptom control<br><br>🧠 <strong>Cognitive Enhancement</strong>: 85% of patients require cognitive enhancement, but limited options are available<br><br>😔 <strong>Negative Symptoms</strong>: 70% experience negative symptom burden with few targeted therapies<br><br>💊 <strong>Adherence</strong>: 40% medication non-adherence rate indicates need for better formulations<br><br>🏠 <strong>Functional Recovery</strong>: 90% experience functional impairment with 55% unemployment rate<br><br>The most critical gaps are in cognitive enhancement and negative symptom management, representing significant opportunities for innovation.`,

                 'how large is the schizophrenia market?': `The schizophrenia market shows strong growth potential:<br><br>💰 <strong>Current Market</strong>: $7.6B (2024)<br>📈 <strong>Growth Rate</strong>: 4.8% CAGR (2023-2030)<br>🎯 <strong>2030 Projection</strong>: $10.7B<br>📊 <strong>7-Year Growth</strong>: 41% total expansion<br><br><strong>Regional Distribution</strong>:<br>🇺🇸 North America: 45% market share<br>🇪🇺 Europe: 32% market share<br>🌏 Asia-Pacific: 18% market share<br>🌍 Rest of World: 5% market share<br><br>The market is driven by novel mechanisms targeting negative and cognitive symptoms, plus long-acting injectable formulations.`,

                 'who are the main competitors?': `The schizophrenia market is highly competitive with several key players:<br><br>🏢 <strong>Major Players</strong> (8 dominant companies):<br>• Johnson & Johnson (Invega franchise)<br>• Otsuka/Lundbeck (Abilify)<br>• Alkermes (Aristada)<br><br>📊 <strong>Market Dynamics</strong>:<br>• 45 active sponsors<br>• 78 Phase II/III trials ongoing<br>• 12 near-term launches expected<br>• 65% generic market share<br><br>⚠️ <strong>Competitive Intensity</strong>: 7.3/10 risk score<br>🌊 <strong>Market Status</strong>: Red ocean (highly saturated)<br>💰 <strong>Challenges</strong>: High pricing pressure and access hurdles<br><br>Innovation focus is shifting toward NMDA receptors, muscarinic agonists, and neuroinflammation pathways.`,

                 'what are the treatment challenges?': `Current schizophrenia treatment faces several significant challenges:<br><br>⚠️ <strong>Efficacy Gaps</strong>:<br>• 60% treatment-resistant cases<br>• Only 25% achieve adequate symptom control<br>• Limited options for cognitive and negative symptoms<br><br>💊 <strong>Adherence Issues</strong>:<br>• 40% medication non-adherence rate<br>• Side effect burden<br>• Complex dosing regimens<br><br>🧠 <strong>Symptom Coverage</strong>:<br>• Positive symptoms: Well-addressed by current antipsychotics<br>• Negative symptoms: Poorly addressed (major unmet need)<br>• Cognitive symptoms: Very limited treatment options<br><br>🏥 <strong>Patient Impact</strong>:<br>• 5.4/10 quality of life score<br>• 90% functional impairment<br>• 55% unemployment rate<br>• 15-20 years reduced life expectancy<br><br>The industry is working on long-acting injectables and novel mechanisms to address these challenges.`
             };

            // Convert to lowercase for matching
            const lowerQuestion = question.toLowerCase();
            
            // Find exact or partial matches
            for (const [key, response] of Object.entries(responses)) {
                if (lowerQuestion.includes(key) || key.includes(lowerQuestion)) {
                    return response;
                }
            }
            
            // Check for keywords
            if (lowerQuestion.includes('unmet') || lowerQuestion.includes('need') || lowerQuestion.includes('gap')) {
                return responses['what are the key unmet needs in schizophrenia?'];
            } else if (lowerQuestion.includes('market') || lowerQuestion.includes('size') || lowerQuestion.includes('revenue')) {
                return responses['how large is the schizophrenia market?'];
            } else if (lowerQuestion.includes('competitor') || lowerQuestion.includes('company') || lowerQuestion.includes('player')) {
                return responses['who are the main competitors?'];
            } else if (lowerQuestion.includes('treatment') || lowerQuestion.includes('challenge') || lowerQuestion.includes('problem')) {
                return responses['what are the treatment challenges?'];
            }
            
                         // Default response
             return `Thank you for your question! Based on our schizophrenia analysis, I can provide insights on:<br><br>🎯 <strong>Unmet Needs</strong>: Treatment resistance, cognitive enhancement, negative symptoms<br>📈 <strong>Market Dynamics</strong>: $7.6B market growing at 4.8% CAGR<br>🏢 <strong>Competitive Landscape</strong>: 45 active sponsors, red ocean market<br>👥 <strong>Patient Profile</strong>: 1.4:1 male predominance, high comorbidities<br>💊 <strong>Treatment Challenges</strong>: 40% non-adherence, limited cognitive options<br><br>Try asking about specific areas like "key unmet needs" or "market size" for detailed insights!`;
        }

        function askSuggestion(question) {
            const input = document.getElementById('chatInput');
            input.value = question;
            sendMessage();
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

                 function openThemeDiscovery() {
             const themeBtn = document.getElementById('themeDiscoveryBtn');
             if (themeBtn.disabled) {
                 return; // Don't open if button is disabled
             }
             
             const modal = document.getElementById('themeModal');
             modal.style.display = 'block';
             document.body.style.overflow = 'hidden';
         }

         function closeThemeDiscovery() {
             const modal = document.getElementById('themeModal');
             modal.style.display = 'none';
             document.body.style.overflow = 'auto';
         }

         function toggleLevel2(element, targetId) {
             const target = document.getElementById(targetId);
             const indicator = element.querySelector('.expand-indicator');
             
             if (target.classList.contains('show')) {
                 target.classList.remove('show');
                 indicator.style.transform = 'rotate(0deg)';
                 element.classList.remove('expanded');
                 
                 // Close all level 3 items within this level 2
                 const level3Items = target.querySelectorAll('.tree-level-3');
                 level3Items.forEach(item => {
                     item.classList.remove('show');
                 });
                 const level2Nodes = target.querySelectorAll('.level-2-node');
                 level2Nodes.forEach(node => {
                     const nodeIndicator = node.querySelector('.expand-indicator');
                     if (nodeIndicator) {
                         nodeIndicator.style.transform = 'rotate(0deg)';
                     }
                 });
             } else {
                 // Close other level 2 items first
                 document.querySelectorAll('.tree-level-2.show').forEach(item => {
                     if (item !== target) {
                         item.classList.remove('show');
                     }
                 });
                 document.querySelectorAll('.level-1-node.expanded').forEach(node => {
                     if (node !== element) {
                         node.classList.remove('expanded');
                         const nodeIndicator = node.querySelector('.expand-indicator');
                         nodeIndicator.style.transform = 'rotate(0deg)';
                     }
                 });
                 
                 target.classList.add('show');
                 indicator.style.transform = 'rotate(90deg)';
                 element.classList.add('expanded');
             }
         }

         function toggleLevel3(element, targetId) {
             const target = document.getElementById(targetId);
             const indicator = element.querySelector('.expand-indicator');
             
             if (target.classList.contains('show')) {
                 target.classList.remove('show');
                 indicator.style.transform = 'rotate(0deg)';
             } else {
                 target.classList.add('show');
                 indicator.style.transform = 'rotate(90deg)';
             }
         }

         // Close modals when clicking outside
         window.onclick = function(event) {
             const kpiModal = document.getElementById('kpiModal');
             const chatModal = document.getElementById('chatbotModal');
             const themeModal = document.getElementById('themeModal');
             
             if (event.target === kpiModal) {
                 closeKPIModal();
             }
             if (event.target === chatModal) {
                 closeChatbot();
             }
             if (event.target === themeModal) {
                 closeThemeDiscovery();
             }
         }

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            showStep(1);
        });
    </script>
</body>
</html>
</html>