Update the existing HTML mockup file `disease_analyzer_mockup_v2.html` to transform it into a comprehensive disease analyzer application interface powered solely by claims data. The mockup should include the following specific sections and features:

**Insight dimension pages should cover Core Analytics Sections:**
1. **Market Basket Validation Page** - Interactive interface where users can review and validate ICD-10 diagnosis codes, NDC drug codes, and CPT procedure codes for their selected disease/condition
2. **Prevalence & Incidence Funnel** - Visual funnel chart displaying patient numbers for diagnosed vs. treated populations, with clear metrics for each stage
3. **High-Level Statistics Dashboard** - Key metrics including:
   - Total patient count
   - Number of healthcare providers (HCPs) treating patients
   - Major medical specialties involved (with percentages)
   - New brand prescriptions (NBRx) and total prescriptions (TRx)
   - Time period breakdowns (monthly/quarterly/yearly)
   - Specialty-specific breakdowns
4. **Lines of Therapy (LOTs) & Duration of Therapy (DOTs) Analysis** - Market share data showing:
   - Line-level treatment shares (1st line, 2nd line, etc.)
   - Competitor product comparisons
   - Average duration of therapy metrics
5. **Patient Journey Visualization** - Comprehensive pathway analysis showing:
   - Common comorbidities
   - Adverse events timeline
   - Procedures and diagnostic tests
   - Different treatment pathways with decision points

**Interactive Chatbot Component:**
Add a chatbot interface with 5-6 predefined questions and answers specifically designed for claims data insights, such as:
- "What is the prevalence of [condition] in my target population?"
- "Which specialists most commonly treat this condition?"
- "What are the most common first-line therapies?"
- "What comorbidities are associated with this condition?"

**Technical Requirements:**
- Maintain the existing visual design and navigation structure
- Add interactive elements for data filtering and exploration
- Include mock data visualizations (charts, graphs, tables)
- Ensure responsive design for different screen sizes
- Add hover states and interactive feedback for better UX